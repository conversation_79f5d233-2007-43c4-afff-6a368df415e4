import React from 'react';
import Sidebar from '../../partials/Sidebar';
import Header from '../../partials/Header';

function <PERSON>nban() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className="flex h-[100dvh] overflow-hidden">
      {/* Sidebar */}
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {/* Page header */}
            <div className="mb-8">
              <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">Kanban Board</h1>
            </div>

            {/* Content */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* To Do Column */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
                  <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                  To Do
                  <span className="ml-auto bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-sm px-2 py-1 rounded">3</span>
                </h3>
                <div className="space-y-3">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-red-500">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Design new landing page</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Create wireframes and mockups for the new product landing page</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 dark:text-gray-400">Due: Dec 15</span>
                      <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded dark:bg-blue-900 dark:text-blue-300">Design</span>
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-red-500">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Update documentation</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Review and update API documentation</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 dark:text-gray-400">Due: Dec 20</span>
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded dark:bg-green-900 dark:text-green-300">Docs</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* In Progress Column */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
                  <span className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                  In Progress
                  <span className="ml-auto bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-sm px-2 py-1 rounded">2</span>
                </h3>
                <div className="space-y-3">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-yellow-500">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Implement user authentication</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Add login and registration functionality</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 dark:text-gray-400">Due: Dec 18</span>
                      <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded dark:bg-purple-900 dark:text-purple-300">Backend</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Done Column */}
              <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4 flex items-center">
                  <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                  Done
                  <span className="ml-auto bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-sm px-2 py-1 rounded">4</span>
                </h3>
                <div className="space-y-3">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-green-500 opacity-75">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Setup project structure</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">Initialize React project with Tailwind CSS</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500 dark:text-gray-400">Completed: Dec 10</span>
                      <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded dark:bg-gray-600 dark:text-gray-300">Setup</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Kanban;
