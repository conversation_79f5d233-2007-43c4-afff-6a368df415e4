import React from 'react';
import Sidebar from '../partials/Sidebar';
import Header from '../partials/Header';

function Messages() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  return (
    <div className="flex h-[100dvh] overflow-hidden">
      {/* Sidebar */}
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {/* Page header */}
            <div className="mb-8">
              <h1 className="text-2xl md:text-3xl text-gray-800 dark:text-gray-100 font-bold">Messages</h1>
            </div>

            {/* Content */}
            <div className="bg-white dark:bg-gray-800 shadow-sm rounded-xl p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">Message Center</h2>
              <div className="flex items-center justify-between mb-4">
                <p className="text-gray-600 dark:text-gray-400">
                  Manage your messages and communications here.
                </p>
                <span className="inline-flex items-center justify-center h-6 text-xs font-medium text-white bg-violet-400 px-2 rounded-sm">
                  4 new
                </span>
              </div>
              <div className="space-y-4">
                <div className="border-l-4 border-violet-500 bg-violet-50 dark:bg-violet-900/20 p-4 rounded">
                  <p className="text-sm text-gray-700 dark:text-gray-300">You have 4 unread messages</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default Messages;
