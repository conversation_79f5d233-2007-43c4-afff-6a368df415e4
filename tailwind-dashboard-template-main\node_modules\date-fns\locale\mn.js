import { formatDistance } from "./mn/_lib/formatDistance.js";
import { formatLong } from "./mn/_lib/formatLong.js";
import { formatRelative } from "./mn/_lib/formatRelative.js";
import { localize } from "./mn/_lib/localize.js";
import { match } from "./mn/_lib/match.js";

/**
 * @category Locales
 * @summary Mongolian locale.
 * @language Mongolian
 * @iso-639-2 mon
 * <AUTHOR> [@bilguun0203](https://github.com/bilguun0203)
 */
export const mn = {
  code: "mn",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default mn;
