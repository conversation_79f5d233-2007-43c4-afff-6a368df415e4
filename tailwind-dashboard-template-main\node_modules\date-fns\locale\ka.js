import { formatDistance } from "./ka/_lib/formatDistance.js";
import { formatLong } from "./ka/_lib/formatLong.js";
import { formatRelative } from "./ka/_lib/formatRelative.js";
import { localize } from "./ka/_lib/localize.js";
import { match } from "./ka/_lib/match.js";

/**
 * @category Locales
 * @summary Georgian locale.
 * @language Georgian
 * @iso-639-2 geo
 * <AUTHOR> [@Landish](https://github.com/Landish)
 * <AUTHOR> [@shvelo](https://github.com/shvelo)
 */
export const ka = {
  code: "ka",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default ka;
