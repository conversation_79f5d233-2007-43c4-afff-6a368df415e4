import { formatDistance } from "./lv/_lib/formatDistance.js";
import { formatLong } from "./lv/_lib/formatLong.js";
import { formatRelative } from "./lv/_lib/formatRelative.js";
import { localize } from "./lv/_lib/localize.js";
import { match } from "./lv/_lib/match.js";

/**
 * @category Locales
 * @summary Latvian locale (Latvia).
 * @language Latvian
 * @iso-639-2 lav
 * <AUTHOR> Puķītis [@prudolfs](https://github.com/prudolfs)
 */
export const lv = {
  code: "lv",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default lv;
